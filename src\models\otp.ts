import mongoose, { Schema, Document, Types } from "mongoose";

interface Otp extends Document {
  otp: string;
  createdAt: Date;
  createdBy: Types.ObjectId;
}

const otpSchema: Schema<Otp> = new Schema(
  {
    otp: { type: String, required: true },
    createdAt: {
      type: Date,
      default: Date.now,
      expires: 60, // The document will be automatically deleted after 1 minute of its creation time
    },
    createdBy: { type: Schema.Types.ObjectId, ref: "User", required: true },
  },
  {
    versionKey: false,
  }
);

const otpModel = mongoose.model<Otp>("Otp", otpSchema);

export default otpModel;
