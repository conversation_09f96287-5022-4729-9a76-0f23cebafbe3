import jwt from 'jsonwebtoken';

// Generate access token
export const generateAccessToken = (userId: string): string => {
    const payload = { userId };
    const accessToken = jwt.sign(payload, process.env.JWT_SECRET as string, {
        expiresIn: process.env.JWT_EXPIRATION_TIME as string,
    });

    return accessToken;
};

// Generate refresh token
export const generateRefreshToken = (userId: string): string => {
    const payload = { userId };
    const refreshToken = jwt.sign(payload, process.env.JWT_REFRESH_SECRET as string, {
        expiresIn: process.env.JWT_EXPIRATION_TIME as string,
    });

    return refreshToken;
};