import mongoose, { Schema, Document, Types } from "mongoose";

interface VerificationToken extends Document {
  token: string;
  email: string;
  userId: Types.ObjectId;
  createdAt: Date;
  expiresAt: Date;
  isUsed: boolean;
}

const verificationTokenSchema: Schema<VerificationToken> = new Schema(
  {
    token: { 
      type: String, 
      required: true, 
      unique: true 
    },
    email: { 
      type: String, 
      required: true 
    },
    userId: { 
      type: Schema.Types.ObjectId, 
      ref: "User", 
      required: true 
    },
    createdAt: {
      type: Date,
      default: Date.now,
    },
    expiresAt: {
      type: Date,
      default: () => new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours from now
      index: { expireAfterSeconds: 0 } // MongoDB will automatically delete expired documents
    },
    isUsed: {
      type: Boolean,
      default: false
    }
  },
  {
    versionKey: false,
  }
);

const verificationTokenModel = mongoose.model<VerificationToken>("VerificationToken", verificationTokenSchema);

export default verificationTokenModel;
