import bcrypt from 'bcrypt';
import { NextFunction, Request, Response } from "express";
import userModel from './../models/User';
import { registerSchema } from "../schemaValidation/user";
import sendOurEmail from "../utils/mailSender";
import verificationTokenModel from '../models/VerificationToken';
import { generateVerificationToken } from '../utils/generateVerificationToken';

export const register = async (req: Request, res: Response, next: NextFunction) => {
  try {
    // Validate request body
    const result = registerSchema.safeParse(req.body);
    if (!result.success) {
      return res.status(400).json({ error: result.error.issues.map((e) => e.message) });
    }
    const { name, email, password } = result.data;
    // Check if user already exists
    const existingUser = await userModel.findOne({ email });
    if (existingUser) {
      return res.status(400).json({ error: "User already exists" });
    }
    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);
    // Create user
    await userModel.create({
      name,
      email,
      password: hashedPassword,
    });
    // Send verification email
    await sendOurEmail(email, "Verify your email");
    // Respond with generic message
    return res.status(201).json({
      message: "User registered successfully. An email was sent to your email so you can verify it."
    });
  } catch (error: any) {
    next({ statusCode: 500, message: error.message || "Internal Server Error" });
  }
};

export const verifyEmail = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const { email } = req.body;
    // Check if email is provided
    if (!email) {
      return res.status(400).json({ error: "Email is required" });
    }
    // Check if user exists
    const user = await userModel.findOne({ email });
    if (!user) {
      return res.status(400).json({ error: "User does not exist" });
    }
    // Update user status
    await userModel.updateOne({ email }, { status: "active" });
    return res.status(200).json({
      message: "Email verified successfully",
    });
  } catch (error: any) {
    next({ statusCode: 500, message: error.message || "Internal Server Error" });
  }
};
