import { Request, Response, NextFunction } from "express";

// نوع مخصص للخطأ يحتوي على رسالة وحالة (اختياري)
interface CustomError extends Error {
  statusCode?: number;
  message: string;
}

export const errorHandler = (
  err: CustomError,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  const statusCode = err.statusCode || 500;
  const message = err.message || "Internal Server Error";
  const stack = process.env.NODE_ENV === "development" ? err.stack : undefined;

  res.status(statusCode).json({
    success: false,
    statusCode,
    message,
    stack,
  });
};
