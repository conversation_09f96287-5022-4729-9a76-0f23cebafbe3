import { createTransport } from "nodemailer";
const sendOurEmail = async (email:string,title:string) => {
    const transporter = createTransport({
        service: "gmail",
        auth: {
            user:  process.env.MAIL_USER,
            pass:  process.env.MAIL_PASS,
        },
    });


    const info = await transporter.sendMail({
        from: '"Note app " <<EMAIL>>', 
        to: email, 
        subject: title, 
        // html: emailTemplate(email)
    });

}

export default sendOurEmail;