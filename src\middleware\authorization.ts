import { Request, Response, NextFunction } from 'express';
import jwt, { JwtPayload, TokenExpiredError } from 'jsonwebtoken';

// Extend Request interface to include user property
declare global {
  namespace Express {
    interface Request {
      user?: JwtPayload;
    }
  }
}

// Helper function to create error objects
const createError = (statusCode: number, message: string) => {
  const error = new Error(message) as any;
  error.statusCode = statusCode;
  return error;
};


// Middleware to check authorization
export const authorize = (req: Request, res: Response, next: NextFunction): void => {
    const authHeader = req.headers['authorization'];
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return next(createError(403, 'Access denied: invalid token format'));
    }

    const token = authHeader.split(' ')[1];

    try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET as string) as JwtPayload;
        req.user = decoded;

        next();
    } catch (error) {
        if (error instanceof TokenExpiredError) {
            return next(createError(401, 'Unauthorized: Token has expired'));
        }
        next(createError(401, 'Unauthorized: Invalid token'));
    }
};

// Middleware to check for admin role
export const authorizeAdmin = (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user || req.user.role !== 'ADMIN') {
        return next(createError(403, 'Access denied: Admin privileges required'));
    }

    next();
};